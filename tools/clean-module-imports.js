import { glob } from 'glob';
import { Project, SyntaxKind } from 'ts-morph';

// Create a project
const project = new Project({
  tsConfigFilePath: 'tsconfig.base.json',
  skipAddingFilesFromTsConfig: true,
});

// Step 1: Find all module files in apps/fincloud/src/app/ directory
const moduleFiles = glob.sync(['apps/fincloud/src/app/**/*.module.ts']);

console.log(`Found ${moduleFiles.length} module files`);

// Step 2: Parse and modify each module file
let modifiedFiles = 0;
let totalElementsRemoved = 0;

for (const moduleFile of moduleFiles) {
  try {
    const sourceFile = project.addSourceFileAtPath(moduleFile);
    let fileModified = false;
    let elementsRemovedFromFile = 0;

    // Find all classes with @NgModule decorator
    const classes = sourceFile.getClasses();

    for (const cls of classes) {
      const ngModuleDecorator = cls.getDecorator('NgModule');

      if (ngModuleDecorator) {
        // Get the decorator argument (should be an object literal)
        const decoratorArgs = ngModuleDecorator.getArguments();

        if (decoratorArgs.length > 0) {
          const configObject = decoratorArgs[0];

          // Check if it's an object literal expression
          if (configObject.getKind() === SyntaxKind.ObjectLiteralExpression) {
            const properties = configObject.getProperties();

            for (const property of properties) {
              if (property.getKind() === SyntaxKind.PropertyAssignment) {
                const propertyAssignment = property;

                // Check if this is the 'imports' property
                const nameNode = propertyAssignment.getNameNode();
                if (nameNode && nameNode.getText() === 'imports') {
                  const initializer = propertyAssignment.getInitializer();

                  if (
                    initializer &&
                    initializer.getKind() === SyntaxKind.ArrayLiteralExpression
                  ) {
                    const arrayLiteral = initializer;
                    const elements = arrayLiteral.getElements();

                    // Collect elements that don't end with 'RoutingModule'
                    const elementsToRemove = [];

                    for (const element of elements) {
                      const elementText = element.getText().trim();
                      if (!elementText.endsWith('RoutingModule')) {
                        elementsToRemove.push(element);
                      }
                    }

                    // Remove elements that don't end with 'RoutingModule'
                    if (elementsToRemove.length > 0) {
                      // Remove elements using the array's removeElement method
                      for (const element of elementsToRemove) {
                        arrayLiteral.removeElement(element);
                      }

                      elementsRemovedFromFile += elementsToRemove.length;
                      fileModified = true;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    // Save the file if it was modified
    if (fileModified) {
      sourceFile.saveSync();
      modifiedFiles++;
      totalElementsRemoved += elementsRemovedFromFile;
    }
  } catch (error) {
    // Skip files that can't be processed
  }
}

// Output summary
console.log(`Modified files: ${modifiedFiles}`);
console.log(`Total elements removed: ${totalElementsRemoved}`);
