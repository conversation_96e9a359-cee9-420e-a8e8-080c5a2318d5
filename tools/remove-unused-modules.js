import fs from 'fs';
import { glob } from 'glob';
import path from 'path';
import { Project } from 'ts-morph';

// Create a project
const project = new Project({
  tsConfigFilePath: 'tsconfig.base.json',
  skipAddingFilesFromTsConfig: true,
});

// Step 1: Find all module files in libs/ directory, excluding swagger-generator
const moduleFiles = glob.sync(['libs/**/*.module.ts'], {
  ignore: ['libs/swagger-generator/**/*'],
});

console.log(`Found ${moduleFiles.length} module files`);

// Step 2: Extract module class names from each file
const moduleInfo = [];

for (const moduleFile of moduleFiles) {
  try {
    const sourceFile = project.addSourceFileAtPath(moduleFile);

    // Find all exported classes that end with 'Module'
    const classes = sourceFile.getClasses().filter((cls) => {
      const name = cls.getName();
      return name && name.endsWith('Module') && cls.isExported();
    });

    if (classes.length > 0) {
      const className = classes[0].getName();
      moduleInfo.push({
        filePath: moduleFile,
        className: className,
        sourceFile: sourceFile,
      });
    }
  } catch (error) {
    // Skip files that can't be processed
  }
}

// Step 3: Delete all modules and update index.ts files

const deletedModules = [];
const updatedIndexFiles = [];

for (const moduleData of moduleInfo) {
  const { filePath, className } = moduleData;

  try {
    // Delete the module file
    fs.unlinkSync(filePath);
    deletedModules.push({ filePath, className });

    // Find and update the corresponding index.ts file
    const libDir = path.dirname(path.dirname(filePath)); // Go up from lib/ to src/
    const indexPath = path.join(libDir, 'index.ts');

    if (fs.existsSync(indexPath)) {
      let indexContent = fs.readFileSync(indexPath, 'utf-8');
      const originalContent = indexContent;

      // Extract the module filename without extension
      const moduleFileName = path.basename(filePath, '.ts');

      // Remove export statements for this module
      // Pattern: export * from './lib/module-name.module';
      const exportPatterns = [
        new RegExp(
          `export\\s*\\*\\s*from\\s*['"]\\./lib/${moduleFileName}['"];?\\s*\\n?`,
          'gm',
        ),
        new RegExp(
          `export\\s*\\*\\s*from\\s*['"]\\./lib/${moduleFileName}['"];?\\s*`,
          'gm',
        ),
        new RegExp(
          `export\\s*{[^}]*}\\s*from\\s*['"]\\./lib/${moduleFileName}['"];?\\s*\\n?`,
          'gm',
        ),
      ];

      for (const pattern of exportPatterns) {
        indexContent = indexContent.replace(pattern, '');
      }

      // Clean up any double newlines
      indexContent = indexContent.replace(/\n\n+/g, '\n\n').trim() + '\n';

      if (indexContent !== originalContent) {
        fs.writeFileSync(indexPath, indexContent, 'utf-8');
        updatedIndexFiles.push(indexPath);
      }
    }
  } catch (error) {
    // Skip files that can't be processed
  }
}

// Output summary
console.log(`Deleted modules: ${deletedModules.length}`);
console.log(`Updated index files: ${updatedIndexFiles.length}`);
