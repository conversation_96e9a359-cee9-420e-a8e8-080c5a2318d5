import fs from 'fs';
import { glob } from 'glob';
import path from 'path';
import { Project } from 'ts-morph';

// Create a project
const project = new Project({
  tsConfigFilePath: 'tsconfig.base.json',
  skipAddingFilesFromTsConfig: true,
});

const files = glob.sync(
  ['libs/components/**/*.ts', 'libs/neoshare/**/*.ts', 'libs/core/**/*.ts'],
  // ['apps/fincloud/src/app/**/*.ts'],
  { ignore: ['**/*.spec.ts', '**/*.module.ts', '**/index.ts'] },
);

// Add source files from libs directory
project.addSourceFilesAtPaths(files);

// Read tsconfig.base.json to get path mappings
const tsConfigPath = path.resolve('tsconfig.base.json');
const tsConfig = JSON.parse(fs.readFileSync(tsConfigPath, 'utf-8'));
const pathMappings = tsConfig.compilerOptions?.paths || {};

// Helper function to extract library name from file path
function getLibraryName(filePath) {
  const match = filePath.match(/libs\/([^\/]+)\/([^\/]+)/);
  return match ? `${match[1]}/${match[2]}` : null;
}

// Helper function to resolve relative path to absolute
function resolveRelativePath(currentFilePath, relativePath) {
  const currentDir = path.dirname(currentFilePath);
  return path.resolve(currentDir, relativePath).replace(/\\/g, '/');
}

// Helper function to find matching path alias
function findPathAlias(targetPath, pathMappings, targetLibrary) {
  if (!pathMappings[`@fincloud/${targetLibrary}`]) return null;

  return `@fincloud/${targetLibrary}`;
  //   for (const [alias, paths] of Object.entries(pathMappings)) {
  //     for (const mappedPath of paths) {
  //       const normalizedMappedPath = path
  //         .resolve(mappedPath.replace('/*', ''))
  //         .replace(/\\/g, '/');
  //       if (targetPath.includes(normalizedMappedPath)) {
  //         return alias.replace('/*', '');
  //       }
  //     }
  //   }
  return null;
}

// Get source files
const sourceFiles = project.getSourceFiles();

// Iterate over each source file
sourceFiles.forEach((sourceFile) => {
  const currentFilePath = sourceFile.getFilePath();
  const currentLibrary = getLibraryName(currentFilePath);

  if (!currentLibrary && !currentFilePath.includes('apps/fincloud/')) return;

  // Get all import declarations
  const importDeclarations = sourceFile.getImportDeclarations();

  // Iterate over each import declaration
  importDeclarations.forEach((importDeclaration) => {
    const moduleSpecifier = importDeclaration.getModuleSpecifierValue();

    // Only process relative imports
    if (
      !moduleSpecifier.startsWith('./') &&
      !moduleSpecifier.startsWith('../')
    ) {
      return;
    }

    // Resolve the relative path to absolute
    const resolvedPath = resolveRelativePath(currentFilePath, moduleSpecifier);

    // Check if this is a cross-library import
    const targetLibrary = getLibraryName(resolvedPath);

    if (targetLibrary && targetLibrary !== currentLibrary) {
      // Find the appropriate path alias
      const pathAlias = findPathAlias(
        resolvedPath,
        pathMappings,
        targetLibrary,
      );

      if (pathAlias) {
        console.log(`Fixing cross-library import in ${currentFilePath}`);
        console.log(`  From: ${moduleSpecifier}`);
        console.log(`  To: ${pathAlias}`);

        // Replace the module specifier
        importDeclaration.setModuleSpecifier(pathAlias);
      } else {
        console.warn(
          `No path alias found for ${resolvedPath} in ${currentFilePath}`,
        );
      }
    }
  });
});

// Save changes
project.saveSync();
console.log('Cross-library import fixes completed.');
