import {
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import { NgClass, NgStyle } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputBaseComponent } from '@fincloud/components/input-base';

import { FocusDirective } from '@fincloud/core/focus';

@Component({
  selector: 'ui-number-input',
  templateUrl: './number-input.component.html',
  styleUrls: ['./number-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    makeControlValueAccessorProvider(NumberInputComponent),
    makeControlValidatorProvider(NumberInputComponent),
  ],
  imports: [FormsModule, NgClass, FocusDirective, NgStyle],
})
export class NumberInputComponent extends InputBaseComponent {
  private readonly _numberPattern = /^[0-9]\d*$/;
  private readonly _allowedActions = [
    'Backspace',
    'Enter',
    'Delete',
    'Control',
    'Home',
    'End',
  ];
  private readonly _allowedKeyCodes = ['KeyA', 'KeyC', 'KeyX'];

  constructor(
    public destroyRef: DestroyRef,
    public changeDetectorRef: ChangeDetectorRef,
  ) {
    super(destroyRef, changeDetectorRef);
  }

  checkForPattern(ev: KeyboardEvent) {
    if (
      !this.isPressedKeyAmongAllowedActions(ev) &&
      !ev.key.match(this._numberPattern)
    ) {
      ev.preventDefault();
      ev.stopImmediatePropagation();
    }
  }

  public onPaste(ev: ClipboardEvent) {
    if (
      this.forbidPaste ||
      !ev.clipboardData.getData('text').match(this._numberPattern)
    ) {
      ev.preventDefault();
    }
  }

  private isPressedKeyAmongAllowedActions(ev: KeyboardEvent) {
    return (
      this._allowedActions.indexOf(ev.code) >= 0 ||
      ev.code?.startsWith('Arrow') ||
      ((ev.ctrlKey || ev.metaKey) &&
        (this._allowedKeyCodes.indexOf(ev.code) >= 0 ||
          (!this.forbidPaste && ev.code === 'KeyV')))
    );
  }
}
