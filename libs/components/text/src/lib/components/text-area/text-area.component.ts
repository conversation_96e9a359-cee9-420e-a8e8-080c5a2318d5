import {
  AbstractValueAccessor,
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import { NgClass, NgStyle } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  Input,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';

import { FocusDirective } from '@fincloud/core/focus';

@Component({
  selector: 'ui-text-area',
  templateUrl: './text-area.component.html',
  styleUrls: ['./text-area.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    makeControlValueAccessorProvider(TextAreaComponent),
    makeControlValidatorProvider(TextAreaComponent),
  ],
  imports: [FormsModule, NgClass, FocusDirective, NgStyle],
})
export class TextAreaComponent
  extends AbstractValueAccessor<string>
  implements AfterViewInit
{
  @Input()
  placeholder = '';

  @Input()
  showSymbolCount = false;

  @Input()
  showShiftEnterInformation = false;

  @Input()
  isDisabled = false;

  @Input()
  isReadonly = false;

  @Input()
  bckgColor: 'background' | 'none' = 'none';

  @Input()
  rows = 8;

  @Input()
  maxlength = 2000;

  @Input()
  contentHeight: number;

  @Input() isInitiallyFocused = false;

  @Input() tabIndex = 0;

  @ViewChild('textarea') textArea: ElementRef<TextAreaComponent>;

  focused: { value: boolean };

  get currentLength() {
    return this.formControl?.value?.length || 0;
  }

  constructor(changeDetectorRef: ChangeDetectorRef) {
    super(changeDetectorRef);
  }

  ngAfterViewInit() {
    this.focused = { value: this.isInitiallyFocused };
  }
}
