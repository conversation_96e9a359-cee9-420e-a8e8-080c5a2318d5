import {
  makeControlValidatorProvider,
  makeControlValueAccessorProvider,
} from '@fincloud/core/form';

import { NgClass, NgStyle } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  ElementRef,
  Input,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputBaseComponent } from '@fincloud/components/input-base';

import { FocusDirective } from '@fincloud/core/focus';

@Component({
  selector: 'ui-text-input',
  templateUrl: './text-input.component.html',
  styleUrls: ['./text-input.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    makeControlValueAccessorProvider(TextInputComponent),
    makeControlValidatorProvider(TextInputComponent),
  ],
  imports: [FormsModule, NgClass, FocusDirective, NgStyle],
})
export class TextInputComponent extends InputBaseComponent {
  @ViewChild('field') inputField: ElementRef;

  @Input()
  type: 'text' | 'email' | 'password' = 'text';

  @Input()
  id: string | null = null;

  @Input()
  uppercase = false;

  @Input()
  lowercase = false;

  @Input()
  showCharsLeft = false;

  @Input()
  blurOnEnter = true;

  @Input()
  autocomplete = '';

  @Input()
  get charsLeft(): number {
    return this.maxLength - (this.value?.length || 0);
  }

  constructor(
    public changeDetectorRef: ChangeDetectorRef,
    public destroyRef: DestroyRef,
  ) {
    super(destroyRef, changeDetectorRef);
  }

  public onInputChanged(change: string) {
    let val = change;

    if (this.uppercase) {
      val = val.toUpperCase();
    }

    if (this.lowercase) {
      val = val.toLowerCase();
    }

    this.value = val;
    this.inputChange.next(val);
  }

  blurField(): void {
    if (!this.blurOnEnter) {
      return;
    }

    this.inputField.nativeElement.blur();
  }
}
