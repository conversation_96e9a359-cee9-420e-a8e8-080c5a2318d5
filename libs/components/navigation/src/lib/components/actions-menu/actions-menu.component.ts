import { MenuTriggerDirective } from '@fincloud/core/overlays';
import { WindowRef } from '@fincloud/core/services';

import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { IconComponent, IconSize } from '@fincloud/components/icons';

import { MenuTriggerDirective as MenuTriggerDirective_1 } from '@fincloud/core/overlays';
@Component({
  selector: 'ui-actions-menu',
  templateUrl: './actions-menu.component.html',
  styleUrls: ['./actions-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent, MenuTriggerDirective_1, NgClass, NgTemplateOutlet],
})
export class ActionsMenuComponent {
  @ViewChild(MenuTriggerDirective, { static: true })
  menuTriggerDirective: MenuTriggerDirective;

  @Input()
  optionsTemplate: TemplateRef<unknown>;

  @Input()
  actionValue: unknown;

  @Input()
  menuOffset = 40;

  @Input()
  inverseArrowOffset = -22;

  @Input()
  iconColor:
    | 'primary'
    | 'subtle'
    | 'regular'
    | 'white'
    | 'dark'
    | 'inherit'
    | 'secondary' = 'regular';

  @Input()
  isInverseArrow = false;

  @Input()
  showMenuBottomAndCenter = false;

  @Input()
  showMenuBottomAndLeft = false;

  @Input()
  customClass = '';

  @Input()
  autoClose = true;

  @Input()
  hideDotsTrigger: boolean;

  @Input()
  hasInteractionState: boolean;

  @Input()
  iconSize: IconSize = 'large';

  @Input()
  customIconClass = '';

  @Input()
  hideArrow: boolean;

  @Input()
  showMenuBottom: boolean;

  @Input()
  isInternalPortal = false;

  @Output()
  visibilityChange: EventEmitter<boolean> = new EventEmitter();

  opened: boolean;

  constructor(private windowRef: WindowRef) {}

  toggleMenu(): void {
    this.opened = !this.opened;
    this.visibilityChange.emit(this.opened);
  }

  closeMenu() {
    this.menuTriggerDirective.toggleOverlay();
  }

  handleCursorType(mouseEvent: MouseEvent) {
    const computedStyleCursor = this.windowRef.nativeWindow.getComputedStyle(
      mouseEvent.target as HTMLElement,
    ).cursor;

    if (
      this.autoClose &&
      (computedStyleCursor === 'pointer' || computedStyleCursor === 'default')
    ) {
      this.closeMenu();
    }
  }
}
