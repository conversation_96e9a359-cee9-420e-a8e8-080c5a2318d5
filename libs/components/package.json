{"name": "@fincloud/components", "peerDependencies": {"@ag-grid-community/angular": "30.1.0", "@ag-grid-community/core": "30.1.0", "@angular/common": "19.2.9", "@angular/core": "19.2.9", "@angular/animations": "19.2.9", "@fincloud/state": "0.0.1", "@fincloud/core": "0.0.1", "@fincloud/types": "0.0.1", "@angular/cdk": "19.2.9", "@ngrx/store": "19.2.1", "rxjs": "7.8.1", "@angular/router": "19.2.9", "azure-maps-control": "3.3.0", "lodash-es": "4.17.21", "azure-maps-rest": "2.1.1", "@angular/forms": "19.2.9", "ngx-scrollbar": "16.1.0", "@ng-bootstrap/ng-bootstrap": "18.0.0", "ngx-currency": "19.0.0", "@angular/platform-browser": "19.2.9", "@swimlane/ngx-charts": "20.5.0", "ng-circle-progress": "1.7.1", "ngx-echarts": "17.2.0", "echarts": "5.5.0", "@fincloud/swagger-generator": "0.0.1", "ngx-ui-tour-ngx-bootstrap": "14.0.0", "hot-formula-parser": "4.0.0", "dayjs": "1.11.11", "@ngx-formly/core": "6.3.3", "angular-cropperjs": "14.0.1", "ngx-infinite-scroll": "19.0.0", "@angular-slider/ngx-slider": "19.0.0", "@siemens/ngx-datatable": "22.4.1", "ngx-spinner": "17.0.0", "ngx-drag-scroll": "19.0.0-rc.0", "ngx-extended-pdf-viewer": "22.3.9", "@ng-select/ng-select": "14.9.0", "@fincloud/ui": "0.9.1-rc.1", "@fincloud/utils": "0.7.1-next.36"}, "sideEffects": false, "version": "0.0.1"}