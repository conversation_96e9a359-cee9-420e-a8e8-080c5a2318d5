import { NgModule } from '@angular/core';

import {
  KeyInformationExtractionEffects,
  keyInformationExtractionFeature,
} from '@fincloud/state/key-information-extraction';
import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalService,
} from '@fincloud/ui/modal';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  BusinessCaseDataRoomEffects,
  businessCaseDataRoomFiltersFeature,
} from './+state';
import { BusinessCaseDataRoomFiltersEffects } from './+state/effects/business-case-data-room-filters.effects';
import { BusinessCaseDataRoomRoutingModule } from './business-case-data-room-routing.module';

@NgModule({
  imports: [BusinessCaseDataRoomRoutingModule],
  providers: [
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
    FIN_MODAL_REF_PROVIDER,
    FinModalService,
    provideState(businessCaseDataRoomFiltersFeature),
    provideState(keyInformationExtractionFeature),
    provideEffects(
      BusinessCaseDataRoomEffects,
      BusinessCaseDataRoomFiltersEffects,
      KeyInformationExtractionEffects,
    ),
  ],
})
export class BusinessCaseDataRoomModule {}
