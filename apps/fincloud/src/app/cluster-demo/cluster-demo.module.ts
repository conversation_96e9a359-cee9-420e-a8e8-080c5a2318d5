import { NgModule } from '@angular/core';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { DemoSnapshotCommonEffects } from './+state/effects/demo-snapshot-common.effects';
import { DemoSnapshotCopyEffects } from './+state/effects/demo-snapshot-copy.effects';
import { DemoSnapshotCreateEffects } from './+state/effects/demo-snapshot-create.effects';
import { DemoSnapshotCustomersEffects } from './+state/effects/demo-snapshot-customers.effects';
import { DemoSnapshotDeleteEffects } from './+state/effects/demo-snapshot-delete.effects';
import { DemoSnapshotDeployEffects } from './+state/effects/demo-snapshot-deploy.effects';
import { DemoSnapshotDetailsCommonEffects } from './+state/effects/demo-snapshot-details-common.effects';
import { DemoSnapshotDetailsLatestStateEffects } from './+state/effects/demo-snapshot-details-latest-state.effects';
import { DemoSnapshotEditGeneralInformationEffects } from './+state/effects/demo-snapshot-edit-general-information.effects';
import { DemoSnapshotEditUserEffects } from './+state/effects/demo-snapshot-edit-user.effects';
import { DemoSnapshotListEffects } from './+state/effects/demo-snapshot-list.effects';
import { DemoSnapshotRecallEffects } from './+state/effects/demo-snapshot-recall.effects';
import { DemoSnapshotResetEffects } from './+state/effects/demo-snapshot-reset.effects';
import { snapshotCreateFeature } from './+state/reducers/demo-snapshot-create.reducer';
import { snapshotDetailsFeature } from './+state/reducers/demo-snapshot-details.reducer';
import {
  demoSnapshotReducer,
  demoSnapshotStateSlice,
} from './+state/reducers/demo-snapshot.reducer';
import { ClusterDemoRoutingModule } from './cluster-demo-routing.module';
import { DemoSnapshotHelperService } from './services/demo-snapshot-helper.service';

@NgModule({
  imports: [ClusterDemoRoutingModule],
  providers: [
    DemoSnapshotHelperService,
    provideState(demoSnapshotStateSlice, demoSnapshotReducer),
    provideState(snapshotDetailsFeature),
    provideState(snapshotCreateFeature),
    provideEffects(
      DemoSnapshotCommonEffects,
      DemoSnapshotCreateEffects,
      DemoSnapshotCustomersEffects,
      DemoSnapshotEditUserEffects,
      DemoSnapshotListEffects,
      DemoSnapshotDetailsCommonEffects,
      DemoSnapshotDetailsLatestStateEffects,
      DemoSnapshotEditGeneralInformationEffects,
      DemoSnapshotCopyEffects,
      DemoSnapshotDeleteEffects,
      DemoSnapshotDeployEffects,
      DemoSnapshotRecallEffects,
      DemoSnapshotResetEffects,
    ),
  ],
})
export class SingleClusterDemoModule {}
