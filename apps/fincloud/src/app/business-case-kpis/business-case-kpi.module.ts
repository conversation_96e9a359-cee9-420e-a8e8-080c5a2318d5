import { <PERSON><PERSON>rencyPipe, DecimalPipe, PercentPipe } from '@angular/common';
import { NgModule } from '@angular/core';

import { BUSINESS_CASE_KPI_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { BusinessCaseKpiEffects, businessCaseKpiStateReducer } from './+state';
import { ManagementSummaryEffects } from './+state/effects/management-summary.effects';
import { BusinessCaseKpiRoutingModule } from './business-case-kpi-routing.module';
@NgModule({
  imports: [BusinessCaseKpiRoutingModule],
  providers: [
    provideState(BUSINESS_CASE_KPI_FEATURE_KEY, businessCaseKpiStateReducer),
    provideEffects(BusinessCaseKpiEffects, ManagementSummaryEffects),
    CurrencyPipe,
    DecimalPipe,
    PercentPipe,
  ],
})
export class BusinessCaseKpiModule {}
