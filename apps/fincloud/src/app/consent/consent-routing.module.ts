import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('@fincloud/components/navigation').then(
        (m) => m.BaseLayoutComponent,
      ),
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./components/terms-of-use/terms-of-use.component').then(
            (m) => m.TermsOfUseComponent,
          ),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class ConsentRoutingModule {}
