import { NgModule } from '@angular/core';
import {
  FolderStructureAddDocumentEffects,
  FolderStructureAddFolderEffects,
  FolderStructureDeleteFolderEffects,
  FolderStructureMoveDocumentEffects,
  FolderStructureMoveFolderEffects,
  FolderStructureRenameFolderEffects,
  folderStructureFeature,
} from '@fincloud/state/folder-structure';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { FolderStructureComponent } from './components/folder-structure/folder-structure.component';

@NgModule({
  imports: [],
  exports: [FolderStructureComponent],
  providers: [
    provideState(folderStructureFeature),
    provideEffects(
      FolderStructureAddFolderEffects,
      FolderStructureRenameFolderEffects,
      FolderStructureDeleteFolderEffects,
      FolderStructureMoveFolderEffects,
      FolderStructureMoveDocumentEffects,
      FolderStructureAddDocumentEffects,
    ),
  ],
})
export class FolderStructureModule {}
