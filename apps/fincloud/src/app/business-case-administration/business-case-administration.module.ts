import { NgModule } from '@angular/core';

import { faqFeature } from '@fincloud/state/faq';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { BusinessCaseAdministrationEffects } from './+state/effects/business-case-administration.effects';
import { BusinessCaseAdministrationRoutingModule } from './business-case-administration-routing.module';

@NgModule({
  imports: [BusinessCaseAdministrationRoutingModule],
  providers: [
    provideState(faqFeature),
    provideEffects([BusinessCaseAdministrationEffects]),
  ],
})
export class BusinessCaseAdministrationModule {}
