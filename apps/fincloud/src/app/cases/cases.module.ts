import { DecimalPipe } from '@angular/common';
import { NgModule } from '@angular/core';

import { CASES_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { casesReducer } from './+state';
import * as effects from './+state/effects';
import { CasesRoutingModule } from './cases-routing.module';

@NgModule({
  imports: [CasesRoutingModule],
  providers: [
    DecimalPipe,
    provideState(CASES_FEATURE_KEY, casesReducer),
    provideEffects(effects.CasesEffects),
  ],
})
export class CasesModule {}
