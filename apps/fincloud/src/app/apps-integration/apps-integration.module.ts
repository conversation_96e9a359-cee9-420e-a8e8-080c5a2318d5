import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { APPS_INTEGRATIONS_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import * as effects from './+state/effects';
import { appIntegrationsReducer } from './+state/reducers/apps-integrations.reducer';

import { appsIntegrationGuard } from './guards/apps-integration.guard';

const routes: Routes = [
  {
    path: '',
    canActivate: [appsIntegrationGuard],
    loadComponent: () =>
      import('./components/apps-integration/apps-integration.component').then(
        (m) => m.AppsIntegrationComponent,
      ),
  },
  {
    path: 'dracoon-integration',
    canActivate: [appsIntegrationGuard],
    loadComponent: () =>
      import(
        './components/dracoon-integration/dracoon-integration.component'
      ).then((m) => m.DracoonIntegrationComponent),
  },
  {
    path: 'nextfolder-integration',
    canActivate: [appsIntegrationGuard],
    loadComponent: () =>
      import(
        './components/nextfolder-integration/nextfolder-integration.component'
      ).then((m) => m.NextfolderIntegrationComponent),
  },
  {
    path: 'core-banking-integration',
    loadComponent: () =>
      import(
        './components/core-banking-integration/core-banking-integration.component'
      ).then((m) => m.CoreBankingIntegrationComponent),
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
  providers: [
    provideState(APPS_INTEGRATIONS_FEATURE_KEY, appIntegrationsReducer),
    provideEffects(effects.AppsIntegrationsEffects),
  ],
})
export class AppsIntegrationModule {}
