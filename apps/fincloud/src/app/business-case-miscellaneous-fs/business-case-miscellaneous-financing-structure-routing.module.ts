import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { FinancingDetailsPath } from '@fincloud/types/enums';

import { financingDetailsMiscellaneousActivateGuard } from './guards/financing-details-miscellaneous-activate.guard';
import { participationMiscellaneousMatchGuard } from './guards/participation-miscellaneous-match.guard';
import { sectionApplicationMiscellaneousMatchGuard } from './guards/section-application-miscellaneous-match.guard';
import { sectionInvitationMiscellaneousMatchGuard } from './guards/section-invitation-miscellaneous-match.guard';
const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/financing-details-miscellaneous/financing-details-miscellaneous.component'
      ).then((m) => m.FinancingDetailsMiscellaneousComponent),
    canActivate: [financingDetailsMiscellaneousActivateGuard],
    children: [
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import(
            './components/participation-miscellaneous/participation-miscellaneous.component'
          ).then((m) => m.ParticipationMiscellaneousComponent),
        canMatch: [participationMiscellaneousMatchGuard],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionApplicationComponent,
          ),
        canMatch: [sectionApplicationMiscellaneousMatchGuard],
      },
      {
        path: FinancingDetailsPath.MY_PARTICIPATION,
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.SectionInvitationComponent,
          ),
        canMatch: [sectionInvitationMiscellaneousMatchGuard],
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class BusinessCaseMiscellaneousFinancingStructureRoutingModule {}
