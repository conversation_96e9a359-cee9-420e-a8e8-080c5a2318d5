import { NgModule } from '@angular/core';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { AccountManagementEffects } from './+state/effects/account-management.effects';
import {
  accountManagementReducer,
  accountManagementStateSlice,
  initialState,
} from './+state/reducers/account-management.reducer';
import { AccountManagementRoutingModule } from './account-management-routing.module';

@NgModule({
  imports: [AccountManagementRoutingModule],
  providers: [
    provideState(accountManagementStateSlice, accountManagementReducer, {
      initialState: initialState,
    }),
    provideEffects(AccountManagementEffects),
  ],
})
export class AccountManagementModule {}
