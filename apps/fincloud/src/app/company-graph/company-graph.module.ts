import { registerLocaleData } from '@angular/common';
import localeDe from '@angular/common/locales/de';
import localeDeExtra from '@angular/common/locales/extra/de';
import { NgModule } from '@angular/core';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { CompanyGraphNetworkEffects } from './+state/effects/company-graph-network.effects';
import { CompanyGraphSettingsEffects } from './+state/effects/company-graph-settings.effects';
import { CompanyGraphUpdateChangesEffects } from './+state/effects/company-graph-update-changes.effects';
import { CompanyGraphVisualizationEffects } from './+state/effects/company-graph-visualization.effects';
import { companyGraphFeature } from './+state/reducers/company-graph.reducer';
import { CompanyGraphRoutingModule } from './company-graph-routing.module';
import { GraphService } from './jointjs/services/graph.service';
import { RadialGraphLayoutService } from './jointjs/services/radial-graph-layout.service';

registerLocaleData(localeDe, 'de-DE', localeDeExtra);

@NgModule({
  imports: [CompanyGraphRoutingModule],
  exports: [],
  providers: [
    GraphService,
    RadialGraphLayoutService,
    provideState(companyGraphFeature),
    provideEffects([
      CompanyGraphNetworkEffects,
      CompanyGraphSettingsEffects,
      CompanyGraphUpdateChangesEffects,
      CompanyGraphVisualizationEffects,
    ]),
  ],
})
export class CompanyGraphModule {}
