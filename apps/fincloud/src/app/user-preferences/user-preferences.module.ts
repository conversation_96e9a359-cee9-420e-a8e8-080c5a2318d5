import { NgModule } from '@angular/core';
import { Routes } from '@angular/router';

import { StateLibUserPreferencesEffects } from '@fincloud/state/user';
import { provideEffects } from '@ngrx/effects';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/user-settings-preferences-tab/user-settings-preferences-tab.component'
      ).then((m) => m.UserSettingsPreferencesTabComponent),
  },
];

@NgModule({
  imports: [],
  providers: [provideEffects(StateLibUserPreferencesEffects)],
})
export class UserPreferencesModule {}
