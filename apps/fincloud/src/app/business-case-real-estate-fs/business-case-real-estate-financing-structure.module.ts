import { NgModule } from '@angular/core';

import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';

import { FINANCING_DETAILS_FEATURE_KEY } from '@fincloud/state/utils';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  FinancingDetailsEffects,
  SearchFinStructureEffects,
} from './+state/effects';
import { TeaserExportEffects } from './+state/effects/teaser-export.effects';
import { reducers } from './+state/reducers';
import { teaserExportFeature } from './+state/reducers/teaser-export.reducer';
import { BusinessCaseRealEstateFinancingStructureRoutingModule } from './business-case-real-estate-financing-structure-routing.module';
import { RefsContentService } from './services/refs-content.service';
import { RefsFinancingPartnersService } from './services/refs-financing-partners.service';
import { SearchFinancingService } from './services/search-financing.service';

@NgModule({
  imports: [BusinessCaseRealEstateFinancingStructureRoutingModule],
  providers: [
    NgbActiveModal,
    provideState(FINANCING_DETAILS_FEATURE_KEY, reducers),
    provideState(teaserExportFeature),
    provideEffects([
      FinancingDetailsEffects,
      SearchFinStructureEffects,
      TeaserExportEffects,
    ]),
    RefsContentService,
    SearchFinancingService,
    RemoveTrailingZerosPipe,
    RefsFinancingPartnersService,
  ],
})
export class BusinessCaseRealEstateFinancingStructureModule {}
