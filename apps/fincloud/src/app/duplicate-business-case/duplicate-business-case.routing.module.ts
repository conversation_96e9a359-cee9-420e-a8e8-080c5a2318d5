import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { duplicateBusinessCaseGuard } from './guards/duplicate-business-case.guard';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/duplicate-business-case/duplicate-business-case.component'
      ).then((m) => m.DuplicateBusinessCaseComponent),
    canActivate: [duplicateBusinessCaseGuard],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class DuplicateBusinessCaseRoutingModule {}
