import { NgModule } from '@angular/core';

import { NgbAccordionDirective } from '@ng-bootstrap/ng-bootstrap';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { DuplicateBusinessCaseEffects } from './+state/effects/duplicate-business-case.effects';
import { duplicateBusinessCaseFeature } from './+state/reducers/duplicate-business-case.reducer';
import { DuplicateBusinessCaseRoutingModule } from './duplicate-business-case.routing.module';

@NgModule({
  imports: [DuplicateBusinessCaseRoutingModule],
  providers: [
    NgbAccordionDirective,
    provideState(duplicateBusinessCaseFeature),
    provideEffects(DuplicateBusinessCaseEffects),
  ],
})
export class DuplicateBusinessCaseModule {}
