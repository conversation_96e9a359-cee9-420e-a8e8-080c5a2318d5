import { <PERSON><PERSON><PERSON>cyPipe, DecimalPipe, PercentPipe } from '@angular/common';
import { NgModule } from '@angular/core';

import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import { DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import * as effects from './+state/effects';
import { dashboardReducer } from './+state/reducers/dashboard.reducer';
import { DashboardRoutingModule } from './dashboard-routing.module';

@NgModule({
  imports: [DashboardRoutingModule],
  providers: [
    DecimalPipe,
    CurrencyPipe,
    PercentPipe,
    RemoveTrailingZerosPipe,
    provideState(DASHBOARD_FEATURE_KEY, dashboardReducer),
    provideEffects(effects.DashboardEffects, effects.ExportExcelEffects),
  ],
})
export class DashboardModule {}
