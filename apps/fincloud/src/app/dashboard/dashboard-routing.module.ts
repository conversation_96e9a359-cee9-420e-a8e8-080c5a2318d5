import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { dashboardGuard } from './guards/dashboard.guard';

const routes: Routes = [
  {
    path: '',
    canActivate: [dashboardGuard],
    loadComponent: () =>
      import('./components/dashboard/dashboard.component').then(
        (m) => m.DashboardComponent,
      ),
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class DashboardRoutingModule {}
