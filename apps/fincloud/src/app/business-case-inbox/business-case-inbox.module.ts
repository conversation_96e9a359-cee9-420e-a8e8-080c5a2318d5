import { NgModule } from '@angular/core';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  BusinessCaseInboxDeleteDocumentEffects,
  BusinessCaseInboxEffects,
  BusinessCaseInboxEmailEffects,
  BusinessCaseInboxOperationsDocumentEffects,
  BusinessCaseInboxUploadDocumentEffects,
  inboxFeature,
} from './+state';
import { businessCaseInboxStorageSyncMetaReducer } from './+state/metareducers/business-case-inbox-storage-sync.metareducer';
import { BusinessCaseInboxRoutingModule } from './business-case-inbox-routing.module';
import { BUSINESS_CASE_INBOX_INITIAL_STATE } from './utils/business-case-inbox-initial-state';

@NgModule({
  imports: [BusinessCaseInboxRoutingModule],
  providers: [
    provideState(inboxFeature.name, inboxFeature.reducer, {
      initialState: BUSINESS_CASE_INBOX_INITIAL_STATE,
      metaReducers: [businessCaseInboxStorageSyncMetaReducer],
    }),
    provideEffects(
      BusinessCaseInboxOperationsDocumentEffects,
      BusinessCaseInboxEmailEffects,
      BusinessCaseInboxEffects,
      BusinessCaseInboxDeleteDocumentEffects,
      BusinessCaseInboxUploadDocumentEffects,
    ),
  ],
})
export class BusinessCaseInboxModule {}
