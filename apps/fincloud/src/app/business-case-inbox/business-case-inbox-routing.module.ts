import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { businessCaseInboxResolver } from './guards/business-case-inbox-resolver';
import { closeDocumentClassificationSocketConnectionGuard } from './guards/close-document-classification-socket-connection.guard';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/business-case-inbox-full-screen/business-case-inbox-full-screen.component'
      ).then((m) => m.BusinessCaseInboxFullScreenComponent),
    resolve: [businessCaseInboxResolver],
    canDeactivate: [closeDocumentClassificationSocketConnectionGuard],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class BusinessCaseInboxRoutingModule {}
