import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('@fincloud/components/navigation').then(
        (m) => m.BaseLayoutComponent,
      ),
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./components/accept-terms/accept-terms.component').then(
            (m) => m.AcceptTermsComponent,
          ),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class TermsAndConditionsRoutingModule {}
