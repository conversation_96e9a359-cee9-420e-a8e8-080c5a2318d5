import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./components/user-management/user-management.component').then(
        (m) => m.UserManagementComponent,
      ),
    children: [
      {
        loadComponent: () =>
          import('./components/user-management/user-management.component').then(
            (m) => m.UserManagementComponent,
          ),
        path: ':userId',
      },
      {
        loadComponent: () =>
          import('./components/user-management/user-management.component').then(
            (m) => m.UserManagementComponent,
          ),
        path: 'create-user',
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class UserManagementModule {}
