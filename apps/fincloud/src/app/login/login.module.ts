import { NgModule } from '@angular/core';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { LoginRoutingModule } from './login-routing.module';

import { clearLoginState } from '@fincloud/state/metareducers';
import { LoginEffects } from './+state/login.effects';
import {
  initialState,
  loginReducer,
  loginStateSlice,
} from './+state/login.reducer';

@NgModule({
  imports: [LoginRoutingModule],
  providers: [
    provideState(loginStateSlice, loginReducer, {
      initialState: initialState,
      metaReducers: [clearLoginState],
    }),
    provideEffects(LoginEffects),
  ],
})
export class LoginModule {}
