import { C<PERSON><PERSON>cyPipe, DatePipe, DecimalPipe } from '@angular/common';
import { NgModule } from '@angular/core';

import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';
import {
  StateLibFinancingStructureEffects,
  businessCaseRealEstateFeature,
} from '@fincloud/state/business-case-real-estate';
import { StateLibCustomerEffects } from '@fincloud/state/customer';
import { StateLibDocumentEffects } from '@fincloud/state/document';
import { faqFeature } from '@fincloud/state/faq';
import {
  KeyInformationExtractionControlEffects,
  keyInformationExtractionControlFeature,
} from '@fincloud/state/key-information-extraction';
import { BUSINESS_CASE_DASHBOARD_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import {
  ActivityLogsEffects,
  AdministrationEffects,
  ApplicationEffects,
  BusinessCaseDataRoomEffects,
  BusinessCaseEffects,
  ChatEffects,
  ChatExportEffects,
  CompanyEffects,
  CompanyPortalEffects,
  CriteriaEffects,
  CustomerEffects,
  FacilityEffects,
  InvitationsEffects,
  PermissionsEffects,
  TeaserExportEffects,
  UserEffects,
} from './+state';
import { BusinessCaseOverviewEffects } from './+state/effects/overview.effects';
import { businessCaseReducers } from './+state/reducers';
import { BusinessCaseDashboardRoutingModule } from './business-case-dashboard-routing.module';

@NgModule({
  imports: [BusinessCaseDashboardRoutingModule],
  providers: [
    provideState(BUSINESS_CASE_DASHBOARD_FEATURE_KEY, businessCaseReducers),
    provideState(faqFeature),
    // TODO: to be moved within business-case-dashboard state, once composeReducers (local impl)
    // is replaced via combineReducers (ngrx impl)
    provideState(businessCaseRealEstateFeature),
    provideState(keyInformationExtractionControlFeature),
    provideEffects(
      BusinessCaseEffects,
      CriteriaEffects,
      ApplicationEffects,
      InvitationsEffects,
      CustomerEffects,
      UserEffects,
      ActivityLogsEffects,
      ChatEffects,
      ChatExportEffects,
      CompanyPortalEffects,
      FacilityEffects,
      CompanyEffects,
      PermissionsEffects,
      StateLibDocumentEffects,
      TeaserExportEffects,
      StateLibCustomerEffects,
      BusinessCaseDataRoomEffects,
      AdministrationEffects,
      StateLibFinancingStructureEffects,
      KeyInformationExtractionControlEffects,
      BusinessCaseOverviewEffects,
    ),
    RemoveTrailingZerosPipe,
    DatePipe,
    CurrencyPipe,
    DecimalPipe,
  ],
})
export class BusinessCaseDashboardModule {}
