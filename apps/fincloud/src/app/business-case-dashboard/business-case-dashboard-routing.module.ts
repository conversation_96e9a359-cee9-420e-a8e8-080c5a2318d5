import { NgModule } from '@angular/core';
import { ActivatedRouteSnapshot, RouterModule, Routes } from '@angular/router';

import {
  faqDataResolver,
  restrictedCaseAccessGuard,
} from '@fincloud/neoshare/guards';
import {
  selectIsBusinessCaseCorporate,
  selectIsBusinessCaseMiscellaneous,
  selectIsBusinessCaseRealEstate,
} from '@fincloud/state/business-case';
import { FinancingDetailsPath } from '@fincloud/types/enums';

import { canActivateAdministrationGuard } from './guards/can-activate-administration.guard';
import { canActivateBusinessCaseOverviewGuard } from './guards/can-activate-business-case-overview.guard';
import { canActivateBusinessCaseGuard } from './guards/can-activate-business-case.guard';
import { CanActivateCaseToCaseRoutingGuard } from './guards/can-activate-case-to-case-routing.guard';
import { canMatchFinancingStructureFactory } from './guards/can-match-financing-structure-factory.guard';
import { duplicateBusinessCaseAccessGuard } from './guards/duplicate-business-case.guard';
import { redirectOnNonMatchingGuard } from './guards/redirect-non-match.guard';

const routes: Routes = [
  {
    path: ':id',
    loadComponent: () =>
      import(
        './components/business-case-dashboard/business-case-dashboard.component'
      ).then((m) => m.BusinessCaseDashboardComponent),
    canActivate: [canActivateBusinessCaseGuard],
    children: [
      {
        path: '',
        redirectTo: 'overview',
        pathMatch: 'full',
      },
      {
        path: 'overview',
        loadComponent: () =>
          import(
            './components/business-case-overview/business-case-overview.component'
          ).then((m) => m.BusinessCaseOverviewComponent),
        canActivate: [canActivateBusinessCaseOverviewGuard],
        children: [
          {
            path: '',
            redirectTo: 'general',
            pathMatch: 'full',
          },
          {
            path: 'kpi-settings',
            loadChildren: () =>
              import('../business-case-kpis/business-case-kpi.module').then(
                (m) => m.BusinessCaseKpiModule,
              ),
          },
          {
            path: 'general',
            loadComponent: () =>
              import(
                './components/business-case-overview-general/business-case-overview-general.component'
              ).then((m) => m.BusinessCaseOverviewGeneralComponent),
            resolve: [faqDataResolver],
          },
        ],
      },
      {
        path: FinancingDetailsPath.FINANCING_DETAILS,
        canMatch: [
          () =>
            canMatchFinancingStructureFactory(selectIsBusinessCaseCorporate),
        ],
        loadChildren: () =>
          import(
            '../business-case-corporate-fs/business-case-corporate-financing-structure.module'
          ).then((m) => m.BusinessCaseCorporateFinancingStructureModule),
      },
      {
        path: FinancingDetailsPath.FINANCING_DETAILS,
        canMatch: [
          () =>
            canMatchFinancingStructureFactory(
              selectIsBusinessCaseMiscellaneous,
            ),
        ],
        loadChildren: () =>
          import(
            '../business-case-miscellaneous-fs/business-case-miscellaneous-financing-structure.module'
          ).then((m) => m.BusinessCaseMiscellaneousFinancingStructureModule),
      },
      {
        path: FinancingDetailsPath.FINANCING_DETAILS,
        canMatch: [
          () =>
            canMatchFinancingStructureFactory(selectIsBusinessCaseRealEstate),
        ],
        loadChildren: () =>
          import(
            '../business-case-real-estate-fs/business-case-real-estate-financing-structure.module'
          ).then((m) => m.BusinessCaseRealEstateFinancingStructureModule),
      },
      {
        path: 'data-room',
        loadChildren: () =>
          import(
            '../business-case-data-room/business-case-data-room.module'
          ).then((m) => m.BusinessCaseDataRoomModule),
      },
      {
        path: 'management',
        canMatch: [canActivateAdministrationGuard],
        loadChildren: () =>
          import(
            '../business-case-administration/business-case-administration.module'
          ).then((m) => m.BusinessCaseAdministrationModule),
      },
      {
        path: 'participant-status',
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.BusinessCaseParticipantRolesComponent,
          ),
      },
      {
        path: 'apply',
        loadComponent: () =>
          import('@fincloud/neoshare/business-case').then(
            (m) => m.BusinessCaseApplyConfirmationModalComponent,
          ),
      },
      {
        path: 'collaboration',
        loadChildren: () =>
          import(
            '../business-case-collaboration/business-case-collaboration.module'
          ).then((m) => m.BusinessCaseCollaborationModule),
      },
    ],
  },
  {
    canActivate: [
      CanActivateCaseToCaseRoutingGuard,
      canActivateBusinessCaseGuard,
    ],
    path: ':id/chat',
    loadChildren: () => import('../chat/chat.module').then((m) => m.ChatModule),
  },
  {
    canActivate: [
      canActivateBusinessCaseGuard,
      duplicateBusinessCaseAccessGuard,
    ],
    path: ':id/duplicate-case',
    loadChildren: () =>
      import('../duplicate-business-case/duplicate-business-case.module').then(
        (m) => m.DuplicateBusinessCaseModule,
      ),
  },
  {
    path: ':id/restricted-access',
    canActivate: [restrictedCaseAccessGuard],
    loadComponent: () =>
      import(
        './components/restricted-case-access/restricted-case-access.component'
      ).then((m) => m.RestrictedCaseAccessComponent),
  },
  // Usually on reload, were data is still fetching , all canMatch will probably
  // return false, since data is not loaded and they can't wait ror it,
  // we need to redirect to same page and pass from canActivate to wait fot the data
  {
    path: '**',
    canActivate: [
      (route: ActivatedRouteSnapshot) => redirectOnNonMatchingGuard(route),
    ],
    children: [],
  },
];
@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class BusinessCaseDashboardRoutingModule {}
