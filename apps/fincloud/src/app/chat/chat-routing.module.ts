import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./components/chat-dashboard/chat-dashboard.component').then(
        (m) => m.ChatDashboardComponent,
      ),
    children: [
      {
        path: 'business-case',
        loadComponent: () =>
          import('./components/chat-dashboard/chat-dashboard.component').then(
            (m) => m.ChatDashboardComponent,
          ),
      },
      {
        path: 'internal',
        loadComponent: () =>
          import('./components/chat-dashboard/chat-dashboard.component').then(
            (m) => m.ChatDashboardComponent,
          ),
        children: [
          {
            path: ':chatId',
            loadComponent: () =>
              import(
                './components/chat-dashboard/chat-dashboard.component'
              ).then((m) => m.ChatDashboardComponent),
          },
        ],
      },
      {
        path: 'bilateral',
        loadComponent: () =>
          import('./components/chat-dashboard/chat-dashboard.component').then(
            (m) => m.ChatDashboardComponent,
          ),
        children: [
          {
            path: ':chatId',
            loadComponent: () =>
              import(
                './components/chat-dashboard/chat-dashboard.component'
              ).then((m) => m.ChatDashboardComponent),
          },
        ],
      },
      {
        path: 'topic',
        loadComponent: () =>
          import('./components/chat-dashboard/chat-dashboard.component').then(
            (m) => m.ChatDashboardComponent,
          ),
        children: [
          {
            path: ':chatId',
            loadComponent: () =>
              import(
                './components/chat-dashboard/chat-dashboard.component'
              ).then((m) => m.ChatDashboardComponent),
          },
        ],
      },
      {
        path: 'archive',
        loadComponent: () =>
          import('./components/chat-dashboard/chat-dashboard.component').then(
            (m) => m.ChatDashboardComponent,
          ),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class ChatRoutingModule {}
