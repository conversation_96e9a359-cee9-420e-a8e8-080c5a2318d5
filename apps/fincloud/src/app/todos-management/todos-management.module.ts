import { NgModule } from '@angular/core';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { TodosManagementEffects } from './+state/effects/todos-management.effects';
import { todosManagementFeature } from './+state/reducers/todos-management.reducer';
import { TodosManagementRoutingModule } from './todos-management-routing.module';
@NgModule({
  imports: [TodosManagementRoutingModule],
  exports: [],
  providers: [
    provideState(todosManagementFeature),
    provideEffects(TodosManagementEffects),
  ],
})
export class TodosManagementModule {}
