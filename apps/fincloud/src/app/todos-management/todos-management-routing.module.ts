import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TodosStatus, TodosType } from '@fincloud/types/enums';

import { checkTodoTypeGuard } from './guards/check-todo-type.guard';
import { checkTodoStatusGuard } from './guards/check-todos-status.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: TodosType.MY_TASKS,
    pathMatch: 'full',
  },
  {
    path: ':todoType',
    canActivate: [checkTodoTypeGuard],
    children: [
      {
        path: '',
        redirectTo: TodosStatus.PENDING,
        pathMatch: 'full',
      },
      {
        path: ':todoStatus',
        canActivate: [checkTodoStatusGuard],
        loadComponent: () =>
          import(
            './components/todos-management-layout/todos-management-layout.component'
          ).then((m) => m.TodosManagementLayoutComponent),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class TodosManagementRoutingModule {}
