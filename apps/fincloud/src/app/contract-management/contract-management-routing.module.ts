import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/contract-management-dashboard/contract-management-dashboard.component'
      ).then((m) => m.ContractManagementDashboardComponent),
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: 'contracts',
      },
      {
        path: 'contracts',
        loadComponent: () =>
          import('./components/contract-list/contract-list.component').then(
            (m) => m.ContractListComponent,
          ),
      },
      // {
      //   path: 'templates',
      //   component: TemplateListComponent,
      // },
      // {
      //   path: 'templates/:id/edit',
      //   component: TextEditorComponent
      // }
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class ContractManagementRoutingModule {}
