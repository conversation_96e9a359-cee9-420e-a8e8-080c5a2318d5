import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
  },
  {
    path: 'new-case-template',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
    data: { newTemplate: true, template: 'case' },
  },
  {
    path: 'new-cadr-template',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
    data: { newTemplate: true, template: 'CADR' },
  },
  {
    path: 'cadr/:id',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
  },
  {
    path: ':id',
    loadComponent: () =>
      import(
        './components/business-case-template-management/business-case-template-management.component'
      ).then((m) => m.BusinessCaseTemplateManagementComponent),
    pathMatch: 'prefix',
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class BusinessCaseTemplateManagementRoutingModule {}
