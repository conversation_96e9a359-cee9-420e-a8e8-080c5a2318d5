import { NgModule } from '@angular/core';

import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import { BusinessCaseTemplateManagementEffects } from './+state/effects/business-case-template-management.effects';
import {
  businessCaseTemplateManagementReducer,
  businessCaseTemplateManagementStateSlice,
  initialTemplatesState,
} from './+state/reducers/business-case-template-management.reducer';
import { BusinessCaseTemplateManagementRoutingModule } from './business-case-template-management-routing.module';

@NgModule({
  imports: [BusinessCaseTemplateManagementRoutingModule],
  exports: [],
  providers: [
    provideState(
      businessCaseTemplateManagementStateSlice,
      businessCaseTemplateManagementReducer,
      {
        initialState: initialTemplatesState,
      },
    ),
    provideEffects(BusinessCaseTemplateManagementEffects),
  ],
})
export class BusinessCaseTemplateManagementModule {}
