import { CommonModule } from '@angular/common';
import { Component, DestroyRef, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AzureMapPopupComponent,
  AzureMapPopupService,
  CompanyBranchesMapComponent,
} from '@fincloud/components/azure-map';

import { ButtonComponent } from '@fincloud/components/buttons';
import { DotsLoaderComponent } from '@fincloud/components/dots-loader';
import { MessagePanelComponent } from '@fincloud/components/message-panel';
import {
  ActionsMenuComponent,
  ActionsMenuItemComponent,
} from '@fincloud/components/navigation';
import { SearchFilterComponent } from '@fincloud/components/search-filter';
import {
  LayoutCommunicationService,
  SidebarLayoutSection,
} from '@fincloud/core/layout';
import { PointOfInterest } from '@fincloud/core/location';
import { ModalService } from '@fincloud/core/modal';
import { ExecuteFuncPipe } from '@fincloud/core/pipes';
import { Toast } from '@fincloud/core/toast';
import { findLocationPopup } from '@fincloud/core/utils';
import {
  CompaniesOnSameAddressResponseDto,
  CompanyBranch,
  CompanyBranchControllerService,
} from '@fincloud/swagger-generator/company';
import { CompanyAnalysisState, Dictionary } from '@fincloud/types/models';
import { FinButtonModule } from '@fincloud/ui/button';
import { FinDropdownOption } from '@fincloud/ui/dropdown';
import { FinScrollbarModule } from '@fincloud/ui/scrollbar';
import { FinSidePanelModule } from '@fincloud/ui/side-panel';
import { FinToastService } from '@fincloud/ui/toast';
import { FinSize } from '@fincloud/ui/types';
import { Store } from '@ngrx/store';
import { cloneDeep, every, map, orderBy, uniqBy } from 'lodash-es';
import { NgScrollbarModule } from 'ngx-scrollbar';
import { Observable, filter, map as mapOperator, of } from 'rxjs';
import {
  selectCompaniesOnSameAddress,
  selectCompanyBranches,
} from '../../+state';
import { CompanyPageActions } from '../../+state/actions';
import { BranchFiltersValue } from '../../models/branch-filters-value';
import { CompanyBranchViewModel } from '../../models/company-branch-view-model';
import { CompanyBranchHelperService } from '../../services/company-branch-helper.service';
import { CompanyBranchEditorComponent } from '../company-branch-editor/company-branch-editor.component';
import { CompanyBranchFiltersComponent } from '../company-branch-filters/company-branch-filters.component';
import { SameAddressCompaniesComponent } from '../same-address-companies/same-address-companies.component';

@Component({
  selector: 'app-company-branches',
  templateUrl: './company-branches.component.html',
  styleUrls: ['./company-branches.component.scss'],
  imports: [
    CommonModule,
    ButtonComponent,
    MessagePanelComponent,
    ActionsMenuComponent,
    ActionsMenuItemComponent,
    NgScrollbarModule,
    SearchFilterComponent,
    CompanyBranchesMapComponent,
    DotsLoaderComponent,
    AzureMapPopupComponent,
    FinSidePanelModule,
    CompanyBranchFiltersComponent,
    FinScrollbarModule,
    FinButtonModule,
    ExecuteFuncPipe,
  ],
  providers: [AzureMapPopupService],
})
export class CompanyBranchesComponent implements OnInit {
  companyBranchesCache: CompanyBranchViewModel[];
  companyBranches: CompanyBranchViewModel[];
  companiesOnSameAddress: Dictionary<CompaniesOnSameAddressResponseDto> = {};
  mapPoints: PointOfInterest[] = [];
  finSize = FinSize;

  searchTerm = '';
  countryOptions: FinDropdownOption[];
  cityOptions: FinDropdownOption[];
  branchFiltersValue: BranchFiltersValue | null = null;
  filtersApplied = false;
  focused$ = of(true);
  loaded = false;

  get hasSearchResults() {
    return this.companyBranches !== null && this.companyBranches?.length > 0;
  }

  get hasNoBranches() {
    return (
      this.companyBranchesCache === null ||
      this.companyBranchesCache?.length === 0
    );
  }

  get messagges() {
    return [$localize`:@@select.notFoundText:Keine Ergebnisse gefunden`];
  }

  activePopupId$: Observable<string> = this.azureMapPopupService.activePopupId$;

  findLocationPopup = findLocationPopup;
  sidebarLayoutSection = SidebarLayoutSection;

  constructor(
    private destroyRef: DestroyRef,
    private store: Store<CompanyAnalysisState>,
    private modalService: ModalService,
    private companyBranchHelperService: CompanyBranchHelperService,
    private companyBranchControllerService: CompanyBranchControllerService,
    private finToastService: FinToastService,
    private azureMapPopupService: AzureMapPopupService,
    private layoutCommunicationService: LayoutCommunicationService,
  ) {}

  ngOnInit(): void {
    this.store
      .select(selectCompanyBranches)
      .pipe(
        filter((branches) => !!branches),
        mapOperator((branches) =>
          branches.map((b) => ({
            ...b,
            formattedAddress:
              this.companyBranchHelperService.buildFormattedAddress(b.address),
          })),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe({
        next: (branches) => {
          this.companyBranchesCache = branches;
          this.setupFilterOptions();
          this.filterBranches();
          this.loaded = true;
        },
      });

    this.store
      .select(selectCompaniesOnSameAddress)
      .pipe(
        filter((c) => !!c),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe({
        next: (sameAddressCompanies) => {
          this.companiesOnSameAddress = sameAddressCompanies;
        },
      });
  }

  filterBranches() {
    const branches = cloneDeep(this.companyBranchesCache || []);

    this.companyBranches = branches.filter((b) => {
      const shouldMatch: boolean[] = [];

      shouldMatch.push(
        !this.searchTerm ||
          new RegExp(this.searchTerm, 'i').test(b.name) ||
          new RegExp(this.searchTerm, 'i').test(b.formattedAddress),
      );
      shouldMatch.push(
        !this.branchFiltersValue?.selectedCountry ||
          new RegExp(this.branchFiltersValue.selectedCountry, 'i').test(
            b.address.country,
          ),
      );
      shouldMatch.push(
        !this.branchFiltersValue?.selectedCity ||
          new RegExp(this.branchFiltersValue.selectedCity, 'i').test(
            b.address.city,
          ),
      );
      shouldMatch.push(
        !this.branchFiltersValue?.selectedBranchTypes?.length ||
          this.branchFiltersValue.selectedBranchTypes.some(
            (branchType) => branchType === b.branchType,
          ),
      );

      return every(shouldMatch, (m) => m === true);
    });

    this.mapPoints =
      this.companyBranches?.map((b) => ({
        label: b.name,
        latitude: b.address.coordinates.lat,
        longitude: b.address.coordinates.lng,
        popup: {
          title: b.name,
          description: b.formattedAddress,
          caseId: b.id,
        },
      })) ?? [];
  }

  setupFilterOptions() {
    this.countryOptions = orderBy(
      uniqBy(
        map(this.companyBranchesCache, (b) => ({
          label: b.address.country,
          value: b.address.country,
        })),
        'label',
      ),
      (o) => o.label,
    );
    this.cityOptions = orderBy(
      uniqBy(
        map(this.companyBranchesCache, (b) => ({
          label: b.address.city,
          value: b.address.city,
        })),
        'label',
      ),
      (o) => o.label,
    );
  }

  onToggleFilters() {
    this.layoutCommunicationService.toggleRightSideOverlayPanel();
  }

  onApplyFilters(filters: {
    filtersValue: BranchFiltersValue;
    hasActiveFilters: boolean;
  }) {
    this.branchFiltersValue = filters.filtersValue;
    this.filtersApplied = filters.hasActiveFilters;
    this.filterBranches();
  }

  openSameAddressCompanies(branch: CompanyBranchViewModel) {
    const companies = (this.companiesOnSameAddress[branch.id] ||
      []) as CompaniesOnSameAddressResponseDto[];
    this.modalService.openComponent(
      SameAddressCompaniesComponent,
      {
        companiesOnSameAddress: companies,
      },
      {
        windowClass: 'same-address-companies-modal',
      },
    );
  }

  onSearch(searchTerm: string) {
    this.searchTerm = searchTerm;
    this.filterBranches();
  }

  countrySelcted() {
    this.filterBranches();
  }

  onBranchTypeSelected() {
    this.filterBranches();
  }

  openManageBranch(branch: CompanyBranch = null) {
    this.modalService.openComponent<CompanyBranchEditorComponent>(
      CompanyBranchEditorComponent,
      {
        companyBranch: branch,
      },
      {
        windowClass: 'company-branch-editor-modal',
        backdrop: true,
        keyboard: true,
        centered: false,
        scrollable: false,
      },
    );
  }

  deleteBranch(branch: CompanyBranch) {
    this.companyBranchControllerService
      .deleteBranch({ branchId: branch.id })
      .subscribe({
        next: () => {
          this.store.dispatch(
            CompanyPageActions.removeCompanyBranch({ payload: branch }),
          );
          this.finToastService.show(Toast.success());
        },
        error: () => this.finToastService.show(Toast.error()),
      });
  }
}
