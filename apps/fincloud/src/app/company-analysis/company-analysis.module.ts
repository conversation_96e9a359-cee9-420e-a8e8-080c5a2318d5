import { DatePip<PERSON>, PercentPipe } from '@angular/common';
import { NgModule } from '@angular/core';

import { RemoveTrailingZerosPipe } from '@fincloud/core/pipes';

import { StateLibDocumentEffects } from '@fincloud/state/document';
import { COMPANY_ANALYSIS_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';

import {
  FIN_MODAL_DEFAULT_OPTIONS,
  FIN_MODAL_REF_PROVIDER,
  FinModalService,
} from '@fincloud/ui/modal';
import { reducers } from './+state';
import * as effects from './+state/effects';
import { CompanyAnalysisRoutingModule } from './company-analysis-routing.module';
import { CompanyModalService } from './services/company-modal.service';

@NgModule({
  imports: [CompanyAnalysisRoutingModule],
  providers: [
    DatePipe,
    PercentPipe,
    RemoveTrailingZerosPipe,
    provideState(COMPANY_ANALYSIS_FEATURE_KEY, reducers),
    provideEffects(
      effects.CompanyEffects,
      StateLibDocumentEffects,
      effects.CompanyDataRoomEffects,
    ),
    CompanyModalService,
    { provide: FIN_MODAL_DEFAULT_OPTIONS, useValue: {} },
    FIN_MODAL_REF_PROVIDER,
    FinModalService,
  ],
})
export class CompanyAnalysisModule {}
