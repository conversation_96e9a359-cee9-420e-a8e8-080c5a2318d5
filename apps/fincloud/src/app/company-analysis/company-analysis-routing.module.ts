import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {
  canAccessCompanyAnalysisDataRoomGuard,
  privateCompanyAnalysisGuard,
} from '@fincloud/neoshare/business-case';
import { companyGuard } from '@fincloud/neoshare/guards';

const routes: Routes = [
  {
    path: ':id',
    redirectTo: ':id/data-room',
  },
  {
    path: ':id',
    canActivate: [companyGuard],
    loadComponent: () =>
      import(
        './components/company-information/company-information.component'
      ).then((m) => m.CompanyInformationComponent),
    children: [
      {
        path: 'data-room',
        canActivate: [
          privateCompanyAnalysisGuard,
          canAccessCompanyAnalysisDataRoomGuard,
        ],
        loadComponent: () =>
          import(
            './components/company-information/company-information.component'
          ).then((m) => m.CompanyInformationComponent),
        children: [
          {
            path: 'own',
            loadComponent: () =>
              import(
                './components/company-information/company-information.component'
              ).then((m) => m.CompanyInformationComponent),
          },
          {
            path: 'shared',
            loadComponent: () =>
              import(
                './components/share-cadr-modal/share-cadr-modal.component'
              ).then((m) => m.ShareCadrModalComponent),
          },
        ],
      },
      {
        path: 'network',
        loadChildren: () =>
          import('../company-graph/company-graph.module').then(
            (m) => m.CompanyGraphModule,
          ),
      },
      {
        path: 'documents',
        loadComponent: () =>
          import(
            './components/company-information/company-information.component'
          ).then((m) => m.CompanyInformationComponent),
      },
      {
        path: 'branches',
        canActivate: [privateCompanyAnalysisGuard],
        loadComponent: () =>
          import(
            './components/company-branches/company-branches.component'
          ).then((m) => m.CompanyBranchesComponent),
      },
      {
        path: 'business-cases',
        loadComponent: () =>
          import(
            './components/other-business-cases/other-business-cases.component'
          ).then((m) => m.OtherBusinessCasesComponent),
      },
      {
        path: 'additional-information',
        loadComponent: () =>
          import(
            './components/company-information-sections/company-information-sections.component'
          ).then((m) => m.CompanyInformationSectionsComponent),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class CompanyAnalysisRoutingModule {}
