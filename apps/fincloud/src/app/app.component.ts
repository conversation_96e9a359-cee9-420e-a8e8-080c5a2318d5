import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PageRibbonComponent } from '@fincloud/components/layout';
import { DocumentPreviewHelperService } from '@fincloud/core/document';
import { DocumentPreviewComponent } from '@fincloud/neoshare/document';
import { FinToastModule } from '@fincloud/ui/toast';

@Component({
  imports: [RouterModule, FinToastModule, PageRibbonComponent],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  constructor(
    private documentPreviewHelperService: DocumentPreviewHelperService,
  ) {}

  ngOnInit(): void {
    // TODO: Temporary solution, use NgTemplateOutlet instead
    this.documentPreviewHelperService.documentPreviewComponent$.next(
      DocumentPreviewComponent,
    );
  }
}
