import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { kpisListGuard } from './guards/kpis-list.guard';

const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import(
        './components/customer-kpi-settings/customer-kpi-settings.component'
      ).then((m) => m.CustomerKpiSettingsComponent),
    canActivate: [kpisListGuard],
    children: [
      {
        path: ':kpiKey',
        loadComponent: () =>
          import(
            './components/customer-kpi-list/customer-kpi-list.component'
          ).then((m) => m.CustomerKpiListComponent),
      },
    ],
  },
];

@NgModule({
  imports: [],
  exports: [RouterModule],
})
export class KpisRoutingModule {}
