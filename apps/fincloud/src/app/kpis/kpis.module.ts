import { NgModule } from '@angular/core';

import { KPIS_FEATURE_KEY } from '@fincloud/state/utils';
import { provideEffects } from '@ngrx/effects';
import { provideState } from '@ngrx/store';
import * as effects from './+state';
import { stateLibKpiStateReducer } from './+state/reducers/kpi.reducer';
import { KpisRoutingModule } from './kpis-routing.module';

@NgModule({
  imports: [KpisRoutingModule],
  providers: [
    provideState(KPIS_FEATURE_KEY, stateLibKpiStateReducer),
    provideEffects(effects.KpiEffects),
  ],
})
export class KpisModule {}
